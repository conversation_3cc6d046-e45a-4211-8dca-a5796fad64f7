// API Interceptor Background Script
// This script intercepts all network requests and stores them for display

import type { RequestData } from "./types";
import { StorageManager } from "./utils/storage";

// Store for ongoing requests
const pendingRequests = new Map<string, RequestData>();

// Capture status
let isCapturing = true;

// Generate unique request ID
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Get request body from details
function getRequestBody(details: chrome.webRequest.WebRequestBodyDetails): string | undefined {
  if (!details.requestBody) return undefined;
  
  if (details.requestBody.formData) {
    return JSON.stringify(details.requestBody.formData);
  }
  
  if (details.requestBody.raw) {
    const decoder = new TextDecoder();
    return details.requestBody.raw
      .map(data => decoder.decode(data.bytes))
      .join('');
  }
  
  return undefined;
}

// Store request data
async function storeRequestData(requestData: RequestData) {
  // Only store if capturing is enabled
  if (!isCapturing) return;

  try {
    await StorageManager.storeRequest(requestData);

    // Notify popup if it's open
    chrome.runtime.sendMessage({
      type: 'NEW_REQUEST',
      data: requestData
    }).catch(() => {
      // Ignore errors if popup is not open
    });
  } catch (error) {
    console.error('Error storing request data:', error);
  }
}

// Listen for request start
chrome.webRequest.onBeforeRequest.addListener(
  (details) => {
    const requestId = generateRequestId();
    const requestData: RequestData = {
      id: requestId,
      url: details.url,
      method: details.method,
      timestamp: Date.now(),
      requestHeaders: [],
      requestBody: getRequestBody(details as chrome.webRequest.WebRequestBodyDetails),
      type: details.type,
      tabId: details.tabId
    };
    
    pendingRequests.set(details.requestId, requestData);
  },
  { urls: ["<all_urls>"] },
  ["requestBody"]
);

// Listen for request headers
chrome.webRequest.onBeforeSendHeaders.addListener(
  (details) => {
    const requestData = pendingRequests.get(details.requestId);
    if (requestData) {
      requestData.requestHeaders = details.requestHeaders || [];
    }
  },
  { urls: ["<all_urls>"] },
  ["requestHeaders"]
);

// Listen for response headers
chrome.webRequest.onHeadersReceived.addListener(
  (details) => {
    const requestData = pendingRequests.get(details.requestId);
    if (requestData) {
      requestData.responseHeaders = details.responseHeaders || [];
      requestData.status = details.statusCode;
      requestData.statusText = details.statusLine;
    }
  },
  { urls: ["<all_urls>"] },
  ["responseHeaders"]
);

// Listen for request completion
chrome.webRequest.onCompleted.addListener(
  (details) => {
    const requestData = pendingRequests.get(details.requestId);
    if (requestData) {
      requestData.duration = Date.now() - requestData.timestamp;
      requestData.status = details.statusCode;
      requestData.statusText = details.statusLine;
      
      // Store the completed request
      storeRequestData(requestData);
      
      // Clean up
      pendingRequests.delete(details.requestId);
    }
  },
  { urls: ["<all_urls>"] }
);

// Listen for request errors
chrome.webRequest.onErrorOccurred.addListener(
  (details) => {
    const requestData = pendingRequests.get(details.requestId);
    if (requestData) {
      requestData.duration = Date.now() - requestData.timestamp;
      requestData.status = 0;
      requestData.statusText = details.error;
      
      // Store the failed request
      storeRequestData(requestData);
      
      // Clean up
      pendingRequests.delete(details.requestId);
    }
  },
  { urls: ["<all_urls>"] }
);

// Handle messages from popup and content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'GET_REQUESTS') {
    StorageManager.getRequests().then(requests => {
      sendResponse({ requests });
    });
    return true; // Keep message channel open for async response
  }

  if (message.type === 'CLEAR_REQUESTS') {
    StorageManager.clearRequests().then(() => {
      sendResponse({ success: true });
    });
    return true;
  }

  if (message.type === 'EXPORT_REQUESTS') {
    StorageManager.exportRequests().then(data => {
      sendResponse({ data });
    }).catch(error => {
      sendResponse({ error: error.message });
    });
    return true;
  }

  if (message.type === 'IMPORT_REQUESTS') {
    StorageManager.importRequests(message.data).then(count => {
      sendResponse({ success: true, importedCount: count });
    }).catch(error => {
      sendResponse({ error: error.message });
    });
    return true;
  }

  if (message.type === 'GET_STORAGE_STATS') {
    StorageManager.getStorageStats().then(stats => {
      sendResponse({ stats });
    });
    return true;
  }

  // Handle capture status
  if (message.type === 'GET_CAPTURE_STATUS') {
    sendResponse({ isCapturing });
    return true;
  }

  if (message.type === 'SET_CAPTURE_STATUS') {
    isCapturing = message.isCapturing;
    sendResponse({ success: true });
    return true;
  }

  // Handle intercepted requests from content script
  if (message.type === 'INTERCEPTED_REQUEST') {
    const interceptedData = message.data;

    // Convert to our RequestData format
    const requestData: RequestData = {
      id: interceptedData.id,
      url: interceptedData.url,
      method: interceptedData.method,
      timestamp: interceptedData.timestamp,
      requestHeaders: Object.entries(interceptedData.requestHeaders || {}).map(([name, value]) => ({ name, value })),
      requestBody: interceptedData.requestBody,
      responseHeaders: Object.entries(interceptedData.responseHeaders || {}).map(([name, value]) => ({ name, value })),
      responseBody: interceptedData.responseBody,
      status: interceptedData.status,
      statusText: interceptedData.statusText,
      type: 'fetch', // Mark as intercepted from content script
      tabId: sender.tab?.id || -1,
      duration: interceptedData.status ? Date.now() - interceptedData.timestamp : undefined
    };

    // Store the intercepted request
    storeRequestData(requestData);

    sendResponse({ success: true });
    return true;
  }

  // Handle replay request
  if (message.type === 'REPLAY_REQUEST') {
    handleReplayRequest(message.requestData)
      .then(result => sendResponse(result))
      .catch(error => sendResponse({ error: error.message }));
    return true; // Keep message channel open for async response
  }
});

// Replay request function
async function handleReplayRequest(requestData: any) {
  try {
    console.log('Background: Replaying request to', requestData.url);

    // Prepare headers
    const headers: Record<string, string> = {};
    if (requestData.headers) {
      requestData.headers.forEach((header: any) => {
        // Skip some headers that browsers handle automatically or cause issues
        const skipHeaders = [
          'host', 'user-agent', 'accept-encoding', 'connection',
          'upgrade-insecure-requests', 'sec-fetch-site', 'sec-fetch-mode',
          'sec-fetch-dest', 'sec-ch-ua', 'sec-ch-ua-mobile', 'sec-ch-ua-platform',
          'origin', 'referer', 'content-length'
        ];
        if (!skipHeaders.includes(header.name.toLowerCase())) {
          headers[header.name] = header.value || '';
        }
      });
    }

    const fetchOptions: RequestInit = {
      method: requestData.method,
      headers: headers,
      mode: 'cors',
      credentials: 'omit'
    };

    // Add body for POST/PUT/PATCH requests
    if (requestData.body && ['POST', 'PUT', 'PATCH'].includes(requestData.method)) {
      fetchOptions.body = requestData.body;
    }

    console.log('Background: Fetch options:', fetchOptions);

    const response = await fetch(requestData.url, fetchOptions);

    // Get response info
    const responseInfo = {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    };

    // Try to get response body
    const contentType = response.headers.get('content-type') || '';
    let responseBody: string;

    if (contentType.includes('application/json')) {
      try {
        const jsonData = await response.json();
        responseBody = JSON.stringify(jsonData, null, 2);
      } catch (e) {
        responseBody = await response.text();
      }
    } else if (contentType.includes('text/') || contentType.includes('application/xml')) {
      responseBody = await response.text();
    } else {
      responseBody = `[${contentType || 'Binary Data'}] - ${response.headers.get('content-length') || 'Unknown'} bytes`;
    }

    return {
      success: true,
      responseInfo,
      responseBody: `Status: ${response.status} ${response.statusText}\n\n${responseBody}`
    };

  } catch (error) {
    console.error('Background: Error replaying request:', error);
    throw error;
  }
}

console.log('API Interceptor background script loaded');
