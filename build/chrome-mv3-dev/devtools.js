// DevTools script for API Interceptor Extension
console.log("DevTools script loaded");

// Create the panel
chrome.devtools.panels.create(
  "API Interceptor",
  "", // Panel icon
  "devtools-panel.html", // Panel HTML file
  function(panel) {
    console.log("API Interceptor DevTools panel created");
    
    panel.onShown.addListener(function(panelWindow) {
      console.log("API Interceptor panel shown");
      if (panelWindow && panelWindow.initPanel) {
        panelWindow.initPanel();
      }
    });
    
    panel.onHidden.addListener(function() {
      console.log("API Interceptor panel hidden");
    });
  }
);

// Listen for network events
chrome.devtools.network.onRequestFinished.addListener(function(request) {
  console.log("DevTools captured request:", request.request.url);
  
  // Send request data to background script
  chrome.runtime.sendMessage({
    type: 'DEVTOOLS_REQUEST',
    data: {
      id: `devtools_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      url: request.request.url,
      method: request.request.method,
      timestamp: Date.now(),
      status: request.response.status,
      statusText: request.response.statusText,
      requestHeaders: request.request.headers || [],
      responseHeaders: request.response.headers || [],
      requestBody: request.request.postData ? request.request.postData.text : null,
      responseBody: null,
      duration: request.time,
      type: 'devtools'
    }
  }).catch(error => {
    console.error("Error sending devtools request:", error);
  });
  
  // Try to get response body
  request.getContent(function(content, encoding) {
    if (content) {
      const requestId = `${request.request.url}_${request.startedDateTime}`;
      chrome.runtime.sendMessage({
        type: 'DEVTOOLS_RESPONSE_BODY',
        requestId: requestId,
        responseBody: content,
        encoding: encoding
      }).catch(error => {
        console.error("Error sending response body:", error);
      });
    }
  });
});
