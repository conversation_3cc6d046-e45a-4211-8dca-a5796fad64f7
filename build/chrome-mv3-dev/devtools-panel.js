// DevTools Panel JavaScript
let requests = [];
let selectedRequest = null;
let responseBodyCache = new Map();

// Initialize panel
function initPanel() {
    console.log('DevTools panel initialized');
    loadStoredRequests();
    setupMessageListener();
}

// Load requests from storage
async function loadStoredRequests() {
    try {
        const response = await chrome.runtime.sendMessage({ type: 'GET_REQUESTS' });
        if (response && response.requests) {
            requests = response.requests.filter(req => req.type === 'devtools');
            renderRequestList();
        }
    } catch (error) {
        console.error('Error loading requests:', error);
    }
}

// Setup message listener for new requests
function setupMessageListener() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === 'DEVTOOLS_REQUEST') {
            addRequest(message.data);
        } else if (message.type === 'DEVTOOLS_RESPONSE_BODY') {
            updateResponseBody(message.requestId, message.responseBody, message.encoding);
        }
    });
}

// Add new request to the list
function addRequest(requestData) {
    requests.unshift(requestData);
    if (requests.length > 1000) {
        requests = requests.slice(0, 1000);
    }
    renderRequestList();
    
    // Store in background
    chrome.runtime.sendMessage({
        type: 'STORE_DEVTOOLS_REQUEST',
        data: requestData
    });
}

// Update response body for a request
function updateResponseBody(requestId, responseBody, encoding) {
    responseBodyCache.set(requestId, { body: responseBody, encoding: encoding });
    
    if (selectedRequest && (selectedRequest.url + '_' + selectedRequest.timestamp) === requestId) {
        selectedRequest.responseBody = responseBody;
        renderRequestDetails(selectedRequest);
    }
}

// Render request list
function renderRequestList() {
    const listElement = document.getElementById('requestList');
    
    if (requests.length === 0) {
        listElement.innerHTML = `
            <div class="empty-state">
                No requests captured yet.<br>
                Navigate to any page to see network requests.
            </div>
        `;
        return;
    }
    
    const html = requests.map(request => {
        const methodClass = `method-${request.method.toLowerCase()}`;
        const isSelected = selectedRequest && selectedRequest.id === request.id;
        
        return `
            <div class="request-item ${isSelected ? 'selected' : ''}" onclick="selectRequest('${request.id}')">
                <div class="request-url">
                    <span class="request-method ${methodClass}">${request.method}</span>
                    ${request.url}
                </div>
                <div class="request-status">
                    Status: ${request.status || 'Pending'} | 
                    ${request.duration ? `${Math.round(request.duration)}ms` : 'N/A'} |
                    ${new Date(request.timestamp).toLocaleTimeString()}
                </div>
            </div>
        `;
    }).join('');
    
    listElement.innerHTML = html;
}

// Select a request
function selectRequest(requestId) {
    selectedRequest = requests.find(req => req.id === requestId);
    if (selectedRequest) {
        const cacheKey = selectedRequest.url + '_' + selectedRequest.timestamp;
        const cachedResponse = responseBodyCache.get(cacheKey);
        if (cachedResponse) {
            selectedRequest.responseBody = cachedResponse.body;
        }
        
        renderRequestDetails(selectedRequest);
        renderRequestList();
    }
}

// Render request details
function renderRequestDetails(request) {
    const detailsElement = document.getElementById('requestDetails');
    
    const requestHeadersHtml = request.requestHeaders ? 
        request.requestHeaders.map(h => `<div><strong>${h.name}:</strong> ${h.value}</div>`).join('') : 
        'No request headers';
    
    const responseHeadersHtml = request.responseHeaders ? 
        request.responseHeaders.map(h => `<div><strong>${h.name}:</strong> ${h.value}</div>`).join('') : 
        'No response headers';
    
    const html = `
        <div class="details-content">
            <div class="details-section">
                <div class="details-title">Request URL</div>
                <div class="details-value">${request.url}</div>
            </div>
            
            <div class="details-section">
                <div class="details-title">Request Method</div>
                <div class="details-value">${request.method}</div>
            </div>
            
            <div class="details-section">
                <div class="details-title">Status Code</div>
                <div class="details-value">${request.status || 'Pending'} ${request.statusText || ''}</div>
            </div>
            
            <div class="details-section">
                <div class="details-title">Request Headers</div>
                <div class="details-value">${requestHeadersHtml}</div>
            </div>
            
            ${request.requestBody ? `
                <div class="details-section">
                    <div class="details-title">Request Body</div>
                    <div class="details-value">${formatJson(request.requestBody)}</div>
                </div>
            ` : ''}
            
            <div class="details-section">
                <div class="details-title">Response Headers</div>
                <div class="details-value">${responseHeadersHtml}</div>
            </div>
            
            <div class="details-section">
                <div class="details-title">Response Body</div>
                <div class="details-value">${request.responseBody ? formatJson(request.responseBody) : 'Loading response body...'}</div>
            </div>
        </div>
    `;
    
    detailsElement.innerHTML = html;
}

// Format JSON if possible
function formatJson(text) {
    try {
        const parsed = JSON.parse(text);
        return JSON.stringify(parsed, null, 2);
    } catch {
        return text;
    }
}

// Clear all requests
function clearRequests() {
    requests = [];
    selectedRequest = null;
    responseBodyCache.clear();
    renderRequestList();
    document.getElementById('requestDetails').innerHTML = `
        <div class="empty-state">
            Select a request to view details
        </div>
    `;
    
    chrome.runtime.sendMessage({ type: 'CLEAR_DEVTOOLS_REQUESTS' });
}

// Export requests
function exportRequests() {
    const dataStr = JSON.stringify(requests, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `devtools-requests-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

// Make initPanel available globally
window.initPanel = initPanel;

// Initialize when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPanel);
} else {
    initPanel();
}
