(function(define){var __define; typeof define === "function" && (__define=define,define=null);
// modules are defined as an array
// [ module function, map of requires ]
//
// map of requires is short require name -> numeric require
//
// anything defined in a previous bundle is accessed via the
// orig method which is the require for previous bundles

(function (modules, entry, mainEntry, parcelRequireName, globalName) {
  /* eslint-disable no-undef */
  var globalObject =
    typeof globalThis !== 'undefined'
      ? globalThis
      : typeof self !== 'undefined'
      ? self
      : typeof window !== 'undefined'
      ? window
      : typeof global !== 'undefined'
      ? global
      : {};
  /* eslint-enable no-undef */

  // Save the require from previous bundle to this closure if any
  var previousRequire =
    typeof globalObject[parcelRequireName] === 'function' &&
    globalObject[parcelRequireName];

  var cache = previousRequire.cache || {};
  // Do not use `require` to prevent Webpack from trying to bundle this call
  var nodeRequire =
    typeof module !== 'undefined' &&
    typeof module.require === 'function' &&
    module.require.bind(module);

  function newRequire(name, jumped) {
    if (!cache[name]) {
      if (!modules[name]) {
        // if we cannot find the module within our internal map or
        // cache jump to the current global require ie. the last bundle
        // that was added to the page.
        var currentRequire =
          typeof globalObject[parcelRequireName] === 'function' &&
          globalObject[parcelRequireName];
        if (!jumped && currentRequire) {
          return currentRequire(name, true);
        }

        // If there are other bundles on this page the require from the
        // previous one is saved to 'previousRequire'. Repeat this as
        // many times as there are bundles until the module is found or
        // we exhaust the require chain.
        if (previousRequire) {
          return previousRequire(name, true);
        }

        // Try the node require function if it exists.
        if (nodeRequire && typeof name === 'string') {
          return nodeRequire(name);
        }

        var err = new Error("Cannot find module '" + name + "'");
        err.code = 'MODULE_NOT_FOUND';
        throw err;
      }

      localRequire.resolve = resolve;
      localRequire.cache = {};

      var module = (cache[name] = new newRequire.Module(name));

      modules[name][0].call(
        module.exports,
        localRequire,
        module,
        module.exports,
        this
      );
    }

    return cache[name].exports;

    function localRequire(x) {
      var res = localRequire.resolve(x);
      return res === false ? {} : newRequire(res);
    }

    function resolve(x) {
      var id = modules[name][1][x];
      return id != null ? id : x;
    }
  }

  function Module(moduleName) {
    this.id = moduleName;
    this.bundle = newRequire;
    this.exports = {};
  }

  newRequire.isParcelRequire = true;
  newRequire.Module = Module;
  newRequire.modules = modules;
  newRequire.cache = cache;
  newRequire.parent = previousRequire;
  newRequire.register = function (id, exports) {
    modules[id] = [
      function (require, module) {
        module.exports = exports;
      },
      {},
    ];
  };

  Object.defineProperty(newRequire, 'root', {
    get: function () {
      return globalObject[parcelRequireName];
    },
  });

  globalObject[parcelRequireName] = newRequire;

  for (var i = 0; i < entry.length; i++) {
    newRequire(entry[i]);
  }

  if (mainEntry) {
    // Expose entry point to Node, AMD or browser globals
    // Based on https://github.com/ForbesLindesay/umd/blob/master/template.js
    var mainExports = newRequire(mainEntry);

    // CommonJS
    if (typeof exports === 'object' && typeof module !== 'undefined') {
      module.exports = mainExports;

      // RequireJS
    } else if (typeof define === 'function' && define.amd) {
      define(function () {
        return mainExports;
      });

      // <script>
    } else if (globalName) {
      this[globalName] = mainExports;
    }
  }
})({"7HYIQ":[function(require,module,exports) {
var d = globalThis.process?.argv || [];
var y = ()=>globalThis.process?.env || {};
var H = new Set(d), _ = (e)=>H.has(e), G = d.filter((e)=>e.startsWith("--") && e.includes("=")).map((e)=>e.split("=")).reduce((e, [t, o])=>(e[t] = o, e), {});
var Z = _("--dry-run"), p = ()=>_("--verbose") || y().VERBOSE === "true", q = p();
var u = (e = "", ...t)=>console.log(e.padEnd(9), "|", ...t);
var x = (...e)=>console.error("\uD83D\uDD34 ERROR".padEnd(9), "|", ...e), v = (...e)=>u("\uD83D\uDD35 INFO", ...e), m = (...e)=>u("\uD83D\uDFE0 WARN", ...e), S = 0, c = (...e)=>p() && u(`\u{1F7E1} ${S++}`, ...e);
var n = {
    "isContentScript": true,
    "isBackground": false,
    "isReact": false,
    "runtimes": [
        "script-runtime"
    ],
    "host": "localhost",
    "port": 1815,
    "entryFilePath": "/Users/<USER>/Projects/api-interceptor-extension/content.ts",
    "bundleId": "3716c965672cac6b",
    "envHash": "e792fbbdaa78ee84",
    "verbose": "false",
    "secure": false,
    "serverPort": 59414
};
module.bundle.HMR_BUNDLE_ID = n.bundleId;
globalThis.process = {
    argv: [],
    env: {
        VERBOSE: n.verbose
    }
};
var D = module.bundle.Module;
function I(e) {
    D.call(this, e), this.hot = {
        data: module.bundle.hotData[e],
        _acceptCallbacks: [],
        _disposeCallbacks: [],
        accept: function(t) {
            this._acceptCallbacks.push(t || function() {});
        },
        dispose: function(t) {
            this._disposeCallbacks.push(t);
        }
    }, module.bundle.hotData[e] = void 0;
}
module.bundle.Module = I;
module.bundle.hotData = {};
var l = globalThis.browser || globalThis.chrome || null;
function b() {
    return !n.host || n.host === "0.0.0.0" ? "localhost" : n.host;
}
function C() {
    return n.port || location.port;
}
var E = "__plasmo_runtime_script_";
function L(e, t) {
    let { modules: o } = e;
    return o ? !!o[t] : !1;
}
function O(e = C()) {
    let t = b();
    return `${n.secure || location.protocol === "https:" && !/localhost|127.0.0.1|0.0.0.0/.test(t) ? "wss" : "ws"}://${t}:${e}/`;
}
function B(e) {
    typeof e.message == "string" && x("[plasmo/parcel-runtime]: " + e.message);
}
function P(e) {
    if (typeof globalThis.WebSocket > "u") return;
    let t = new WebSocket(O());
    return t.addEventListener("message", async function(o) {
        let r = JSON.parse(o.data);
        if (r.type === "update" && await e(r.assets), r.type === "error") for (let a of r.diagnostics.ansi){
            let w = a.codeframe || a.stack;
            m("[plasmo/parcel-runtime]: " + a.message + `
` + w + `

` + a.hints.join(`
`));
        }
    }), t.addEventListener("error", B), t.addEventListener("open", ()=>{
        v(`[plasmo/parcel-runtime]: Connected to HMR server for ${n.entryFilePath}`);
    }), t.addEventListener("close", ()=>{
        m(`[plasmo/parcel-runtime]: Connection to the HMR server is closed for ${n.entryFilePath}`);
    }), t;
}
var s = "__plasmo-loading__";
function $() {
    let e = globalThis.window?.trustedTypes;
    if (typeof e > "u") return;
    let t = document.querySelector('meta[name="trusted-types"]')?.content?.split(" "), o = t ? t[t?.length - 1].replace(/;/g, "") : void 0;
    return typeof e < "u" ? e.createPolicy(o || `trusted-html-${s}`, {
        createHTML: (a)=>a
    }) : void 0;
}
var T = $();
function g() {
    return document.getElementById(s);
}
function f() {
    return !g();
}
function F() {
    let e = document.createElement("div");
    e.id = s;
    let t = `
  <style>
    #${s} {
      background: #f3f3f3;
      color: #333;
      border: 1px solid #333;
      box-shadow: #333 4.7px 4.7px;
    }

    #${s}:hover {
      background: #e3e3e3;
      color: #444;
    }

    @keyframes plasmo-loading-animate-svg-fill {
      0% {
        fill: transparent;
      }
    
      100% {
        fill: #333;
      }
    }

    #${s} .svg-elem-1 {
      animation: plasmo-loading-animate-svg-fill 1.47s cubic-bezier(0.47, 0, 0.745, 0.715) 0.8s both infinite;
    }

    #${s} .svg-elem-2 {
      animation: plasmo-loading-animate-svg-fill 1.47s cubic-bezier(0.47, 0, 0.745, 0.715) 0.9s both infinite;
    }
    
    #${s} .svg-elem-3 {
      animation: plasmo-loading-animate-svg-fill 1.47s cubic-bezier(0.47, 0, 0.745, 0.715) 1s both infinite;
    }

    #${s} .hidden {
      display: none;
    }

  </style>
  
  <svg height="32" width="32" viewBox="0 0 264 354" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M139.221 282.243C154.252 282.243 166.903 294.849 161.338 308.812C159.489 313.454 157.15 317.913 154.347 322.109C146.464 333.909 135.26 343.107 122.151 348.538C109.043 353.969 94.6182 355.39 80.7022 352.621C66.7861 349.852 54.0034 343.018 43.9705 332.983C33.9375 322.947 27.105 310.162 24.3369 296.242C21.5689 282.323 22.9895 267.895 28.4193 254.783C33.8491 241.671 43.0441 230.464 54.8416 222.579C59.0353 219.777 63.4908 217.438 68.1295 215.588C82.0915 210.021 94.6978 222.671 94.6978 237.703L94.6978 255.027C94.6978 270.058 106.883 282.243 121.914 282.243H139.221Z" fill="#333" class="svg-elem-1" ></path>
    <path d="M192.261 142.028C192.261 126.996 204.867 114.346 218.829 119.913C223.468 121.763 227.923 124.102 232.117 126.904C243.915 134.789 253.11 145.996 258.539 159.108C263.969 172.22 265.39 186.648 262.622 200.567C259.854 214.487 253.021 227.272 242.988 237.308C232.955 247.343 220.173 254.177 206.256 256.946C192.34 259.715 177.916 258.294 164.807 252.863C151.699 247.432 140.495 238.234 132.612 226.434C129.808 222.238 127.47 217.779 125.62 213.137C120.056 199.174 132.707 186.568 147.738 186.568L165.044 186.568C180.076 186.568 192.261 174.383 192.261 159.352L192.261 142.028Z" fill="#333" class="svg-elem-2" ></path>
    <path d="M95.6522 164.135C95.6522 179.167 83.2279 191.725 68.8013 187.505C59.5145 184.788 50.6432 180.663 42.5106 175.227C26.7806 164.714 14.5206 149.772 7.28089 132.289C0.041183 114.807 -1.85305 95.5697 1.83772 77.0104C5.52849 58.4511 14.6385 41.4033 28.0157 28.0228C41.393 14.6423 58.4366 5.53006 76.9914 1.83839C95.5461 -1.85329 114.779 0.0414162 132.257 7.2829C149.735 14.5244 164.674 26.7874 175.184 42.5212C180.62 50.6576 184.744 59.5332 187.46 68.8245C191.678 83.2519 179.119 95.6759 164.088 95.6759L122.869 95.6759C107.837 95.6759 95.6522 107.861 95.6522 122.892L95.6522 164.135Z" fill="#333" class="svg-elem-3"></path>
  </svg>
  <span class="hidden">Context Invalidated, Press to Reload</span>
  `;
    return e.innerHTML = T ? T.createHTML(t) : t, e.style.pointerEvents = "none", e.style.position = "fixed", e.style.bottom = "14.7px", e.style.right = "14.7px", e.style.fontFamily = "sans-serif", e.style.display = "flex", e.style.justifyContent = "center", e.style.alignItems = "center", e.style.padding = "14.7px", e.style.gap = "14.7px", e.style.borderRadius = "4.7px", e.style.zIndex = "2147483647", e.style.opacity = "0", e.style.transition = "all 0.47s ease-in-out", e;
}
function N(e) {
    return new Promise((t)=>{
        document.documentElement ? (f() && (document.documentElement.appendChild(e), t()), t()) : globalThis.addEventListener("DOMContentLoaded", ()=>{
            f() && document.documentElement.appendChild(e), t();
        });
    });
}
var k = ()=>{
    let e;
    if (f()) {
        let t = F();
        e = N(t);
    }
    return {
        show: async ({ reloadButton: t = !1 } = {})=>{
            await e;
            let o = g();
            o.style.opacity = "1", t && (o.onclick = (r)=>{
                r.stopPropagation(), globalThis.location.reload();
            }, o.querySelector("span").classList.remove("hidden"), o.style.cursor = "pointer", o.style.pointerEvents = "all");
        },
        hide: async ()=>{
            await e;
            let t = g();
            t.style.opacity = "0";
        }
    };
};
var W = `${E}${module.id}__`, i, A = !1, M = k();
async function h() {
    c("Script Runtime - reloading"), A ? globalThis.location?.reload?.() : M.show({
        reloadButton: !0
    });
}
function R() {
    i?.disconnect(), i = l?.runtime.connect({
        name: W
    }), i.onDisconnect.addListener(()=>{
        h();
    }), i.onMessage.addListener((e)=>{
        e.__plasmo_cs_reload__ && h(), e.__plasmo_cs_active_tab__ && (A = !0);
    });
}
function j() {
    if (l?.runtime) try {
        R(), setInterval(R, 24e3);
    } catch  {
        return;
    }
}
j();
P(async (e)=>{
    c("Script runtime - on updated assets"), e.filter((o)=>o.envHash === n.envHash).some((o)=>L(module.bundle, o.id)) && (M.show(), l?.runtime ? i.postMessage({
        __plasmo_cs_changed__: !0
    }) : setTimeout(()=>{
        h();
    }, 4700));
});

},{}],"71ecL":[function(require,module,exports) {
// Content script to intercept fetch and XMLHttpRequest responses
// This script runs in the context of web pages to capture response bodies
// Generate unique request ID
function generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
// Send intercepted data to background script
function sendToBackground(data) {
    try {
        chrome.runtime.sendMessage({
            type: "INTERCEPTED_REQUEST",
            data: data
        }).catch(()=>{
        // Ignore errors if extension context is invalid
        });
    } catch (error) {
        console.error("Error sending intercepted request:", error);
    }
}
// Intercept fetch API
const originalFetch = window.fetch;
window.fetch = async function(...args) {
    const startTime = Date.now();
    const requestId = generateRequestId();
    // Parse request details
    const [input, init] = args;
    const url = typeof input === "string" ? input : input.url;
    const method = init?.method || "GET";
    // Get request headers
    const requestHeaders = {};
    if (init?.headers) {
        if (init.headers instanceof Headers) init.headers.forEach((value, key)=>{
            requestHeaders[key] = value;
        });
        else if (Array.isArray(init.headers)) init.headers.forEach(([key, value])=>{
            requestHeaders[key] = value;
        });
        else Object.entries(init.headers).forEach(([key, value])=>{
            requestHeaders[key] = value;
        });
    }
    // Get request body
    let requestBody;
    if (init?.body) {
        if (typeof init.body === "string") requestBody = init.body;
        else if (init.body instanceof FormData) requestBody = "[FormData]";
        else if (init.body instanceof URLSearchParams) requestBody = init.body.toString();
        else requestBody = "[Binary Data]";
    }
    try {
        // Make the actual request
        const response = await originalFetch.apply(this, args);
        // Clone response to read body without consuming it
        const responseClone = response.clone();
        // Get response headers
        const responseHeaders = {};
        response.headers.forEach((value, key)=>{
            responseHeaders[key] = value;
        });
        // Try to read response body
        let responseBody;
        try {
            const contentType = response.headers.get("content-type") || "";
            if (contentType.includes("application/json") || contentType.includes("text/") || contentType.includes("application/xml")) responseBody = await responseClone.text();
            else responseBody = `[${contentType || "Binary Data"}]`;
        } catch (error) {
            responseBody = "[Error reading response body]";
        }
        // Send intercepted data
        const interceptedData = {
            id: requestId,
            url: url,
            method: method,
            timestamp: startTime,
            requestHeaders,
            requestBody,
            responseHeaders,
            responseBody,
            status: response.status,
            statusText: response.statusText
        };
        sendToBackground(interceptedData);
        return response;
    } catch (error) {
        // Send error data
        const interceptedData = {
            id: requestId,
            url: url,
            method: method,
            timestamp: startTime,
            requestHeaders,
            requestBody,
            status: 0,
            statusText: error instanceof Error ? error.message : "Network Error"
        };
        sendToBackground(interceptedData);
        throw error;
    }
};
// Intercept XMLHttpRequest
const originalXHROpen = XMLHttpRequest.prototype.open;
const originalXHRSend = XMLHttpRequest.prototype.send;
XMLHttpRequest.prototype.open = function(method, url, ...args) {
    this._interceptorData = {
        id: generateRequestId(),
        method: method.toUpperCase(),
        url: url,
        timestamp: Date.now()
    };
    return originalXHROpen.apply(this, [
        method,
        url,
        ...args
    ]);
};
XMLHttpRequest.prototype.send = function(body) {
    const interceptorData = this._interceptorData;
    if (!interceptorData) return originalXHRSend.apply(this, [
        body
    ]);
    // Store request body
    if (body) {
        if (typeof body === "string") interceptorData.requestBody = body;
        else if (body instanceof FormData) interceptorData.requestBody = "[FormData]";
        else interceptorData.requestBody = "[Binary Data]";
    }
    // Store request headers
    interceptorData.requestHeaders = {};
    // Listen for response
    const originalOnReadyStateChange = this.onreadystatechange;
    this.onreadystatechange = function() {
        if (this.readyState === XMLHttpRequest.DONE) {
            // Get response headers
            const responseHeaders = {};
            const headerString = this.getAllResponseHeaders();
            if (headerString) headerString.split("\r\n").forEach((line)=>{
                const [key, value] = line.split(": ");
                if (key && value) responseHeaders[key] = value;
            });
            // Get response body
            let responseBody;
            try {
                const contentType = this.getResponseHeader("content-type") || "";
                if (contentType.includes("application/json") || contentType.includes("text/") || contentType.includes("application/xml")) responseBody = this.responseText;
                else responseBody = `[${contentType || "Binary Data"}]`;
            } catch (error) {
                responseBody = "[Error reading response body]";
            }
            // Send intercepted data
            const finalData = {
                ...interceptorData,
                responseHeaders,
                responseBody,
                status: this.status,
                statusText: this.statusText
            };
            sendToBackground(finalData);
        }
        if (originalOnReadyStateChange) originalOnReadyStateChange.apply(this);
    };
    return originalXHRSend.apply(this, [
        body
    ]);
};
console.log("API Interceptor content script loaded");

},{}]},["7HYIQ","71ecL"], "71ecL", "parcelRequireff35")

//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
 globalThis.define=__define;  })(globalThis.define);