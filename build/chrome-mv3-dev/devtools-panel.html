<!DOCTYPE html>
<html>
<head>
    <title>API Interceptor Panel</title>
    <meta charset="utf-8">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .controls {
            display: flex;
            gap: 8px;
        }
        
        .btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn:hover {
            background: #f0f0f0;
        }
        
        .btn.primary {
            background: #1976d2;
            color: white;
            border-color: #1976d2;
        }
        
        .btn.primary:hover {
            background: #1565c0;
        }
        
        .content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        
        .request-list {
            width: 50%;
            border-right: 1px solid #e0e0e0;
            background: white;
            overflow-y: auto;
        }
        
        .request-details {
            width: 50%;
            background: white;
            overflow-y: auto;
        }
        
        .request-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .request-item:hover {
            background-color: #f8f9fa;
        }
        
        .request-item.selected {
            background-color: #e3f2fd;
        }
        
        .request-method {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 600;
            margin-right: 8px;
        }
        
        .method-get { background: #e8f5e8; color: #2e7d32; }
        .method-post { background: #e3f2fd; color: #1976d2; }
        .method-put { background: #fff3e0; color: #f57c00; }
        .method-delete { background: #ffebee; color: #d32f2f; }
        
        .request-url {
            font-size: 13px;
            color: #333;
            margin-bottom: 4px;
            word-break: break-all;
        }
        
        .request-status {
            font-size: 12px;
            color: #666;
        }
        
        .details-content {
            padding: 16px;
        }
        
        .details-section {
            margin-bottom: 24px;
        }
        
        .details-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .details-value {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .empty-state {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            font-size: 14px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">API Interceptor - DevTools Panel</div>
            <div class="controls">
                <button class="btn" onclick="clearRequests()">Clear</button>
                <button class="btn primary" onclick="exportRequests()">Export</button>
            </div>
        </div>
        
        <div class="content">
            <div class="request-list" id="requestList">
                <div class="empty-state">
                    No requests captured yet.<br>
                    Navigate to any page to see network requests.
                </div>
            </div>
            
            <div class="request-details" id="requestDetails">
                <div class="empty-state">
                    Select a request to view details
                </div>
            </div>
        </div>
    </div>
    
    <script src="devtools-panel.js"></script>
</body>
</html>
