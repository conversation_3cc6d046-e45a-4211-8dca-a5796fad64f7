<!DOCTYPE html>
<html>
<head>
    <title>API Interceptor Panel</title>
    <meta charset="utf-8">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }

        .test-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .status {
            color: #28a745;
            font-weight: bold;
            margin-bottom: 10px;
        }

    </style>
</head>
<body>
    <div class="test-panel">
        <div class="status">✅ API Interceptor DevTools Panel Loaded Successfully!</div>
        <p>This is a test version of the API Interceptor DevTools panel.</p>
        <p>If you can see this message, the DevTools integration is working correctly.</p>

        <div id="requests-container">
            <h3>Network Requests:</h3>
            <div id="requests-list">No requests captured yet...</div>
        </div>

        <button onclick="testFunction()" style="margin-top: 10px; padding: 8px 16px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Test Button
        </button>
    </div>

    <script>
        console.log("🎨 DevTools panel HTML loaded successfully");

        function testFunction() {
            alert("🎉 DevTools panel is working perfectly!");
            console.log("✅ Test function called from DevTools panel");

            // Test chrome.runtime connection
            if (chrome && chrome.runtime) {
                chrome.runtime.sendMessage({type: 'TEST_FROM_DEVTOOLS'}, function(response) {
                    console.log("📡 Message sent to background script, response:", response);
                });
            }
        }

        function initPanel() {
            console.log("🚀 DevTools panel initialized");
            const timestamp = new Date().toLocaleTimeString();
            document.getElementById('requests-list').innerHTML = `
                <div style="color: green; font-weight: bold;">✅ Panel initialized at ${timestamp}</div>
                <div style="margin-top: 10px;">
                    <strong>Debug Info:</strong><br>
                    - Chrome Runtime: ${chrome && chrome.runtime ? '✅ Available' : '❌ Not Available'}<br>
                    - DevTools: ${chrome && chrome.devtools ? '✅ Available' : '❌ Not Available'}<br>
                    - Location: ${window.location.href}<br>
                    - User Agent: ${navigator.userAgent.substring(0, 50)}...
                </div>
            `;
        }

        // Make initPanel available globally
        window.initPanel = initPanel;

        // Auto-initialize
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initPanel);
        } else {
            initPanel();
        }

        // Additional debugging
        console.log("🔍 Panel context check:", {
            chrome: typeof chrome,
            runtime: typeof chrome?.runtime,
            devtools: typeof chrome?.devtools,
            location: window.location?.href
        });
    </script>
</body>
</html>
