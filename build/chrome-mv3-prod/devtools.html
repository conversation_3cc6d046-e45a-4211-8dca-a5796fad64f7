<!DOCTYPE html>
<html>
<head>
    <title>API Interceptor DevTools</title>
    <meta charset="utf-8">
</head>
<body>
    <script>
        // DevTools script for API Interceptor Extension
        console.log("🚀 API Interceptor DevTools script loaded");

        // Create the panel
        chrome.devtools.panels.create(
          "API Interceptor",
          "", // Panel icon
          "devtools-panel.html", // Panel HTML file
          function(panel) {
            console.log("✅ API Interceptor DevTools panel created");

            panel.onShown.addListener(function(panelWindow) {
              console.log("👁️ API Interceptor panel shown");
              if (panelWindow && panelWindow.initPanel) {
                panelWindow.initPanel();
              }
            });

            panel.onHidden.addListener(function() {
              console.log("👁️‍🗨️ API Interceptor panel hidden");
            });
          }
        );

        // Listen for network events and capture requests with response bodies
        chrome.devtools.network.onRequestFinished.addListener(function(request) {
          console.log("🌐 Network request finished:", request.request.url, "Method:", request.request.method, "Status:", request.response.status);

          // Skip certain requests to reduce noise
          const url = request.request.url;
          if (url.includes('chrome-extension://') ||
              url.includes('data:') ||
              url.includes('.css') ||
              url.includes('.js') ||
              url.includes('.png') ||
              url.includes('.jpg') ||
              url.includes('.gif') ||
              url.includes('.ico')) {
            console.log("⏭️ Skipping static resource:", url);
            return;
          }

          // Create unique request ID
          const requestId = `devtools_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

          // Create request data object
          const requestData = {
            id: requestId,
            url: request.request.url,
            method: request.request.method,
            timestamp: new Date(request.startedDateTime).getTime(),
            status: request.response.status,
            statusText: request.response.statusText,
            requestHeaders: request.request.headers || [],
            responseHeaders: request.response.headers || [],
            requestBody: request.request.postData ? request.request.postData.text : null,
            responseBody: null,
            duration: request.time,
            type: 'devtools',
            mimeType: request.response.content?.mimeType || 'unknown',
            size: request.response.content?.size || 0
          };

          console.log("📋 Created request data:", {
            id: requestData.id,
            url: requestData.url,
            method: requestData.method,
            status: requestData.status
          });

          // Get response body asynchronously
          request.getContent(function(content, encoding) {
            if (content) {
              requestData.responseBody = content;
              requestData.encoding = encoding;
              console.log("📦 Response body captured for:", request.request.url, `(${content.length} chars)`);
            } else {
              console.log("⚠️ No response body for:", request.request.url);
            }

            console.log("📤 Sending request data to background script...");

            // Send complete request data to background script
            chrome.runtime.sendMessage({
              type: 'DEVTOOLS_REQUEST_COMPLETE',
              data: requestData
            }).then(response => {
              console.log("✅ Successfully sent request data, response:", response);
            }).catch(error => {
              console.error("❌ Error sending complete request data:", error);
            });
          });
        });

        console.log("🔧 DevTools script setup complete");
    </script>
</body>
</html>