{"icons": {"16": "icon16.plasmo.6c567d50.png", "32": "icon32.plasmo.76b92899.png", "48": "icon48.plasmo.aced7582.png", "64": "icon64.plasmo.8bb5e6e0.png", "128": "icon128.plasmo.3c1ed2d2.png"}, "manifest_version": 3, "action": {"default_icon": {"16": "icon16.plasmo.6c567d50.png", "32": "icon32.plasmo.76b92899.png", "48": "icon48.plasmo.aced7582.png", "64": "icon64.plasmo.8bb5e6e0.png", "128": "icon128.plasmo.3c1ed2d2.png"}, "default_popup": "popup.html"}, "version": "0.0.1", "author": "Plasmo Corp. <<EMAIL>>", "name": "Api interceptor extension", "description": "A Chrome extension to intercept and display all API requests with beautiful UI", "background": {"service_worker": "static/background/index.js"}, "options_ui": {"page": "options.html", "open_in_tab": true}, "permissions": ["webRequest", "storage", "activeTab", "devtools"], "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.06af5f40.js"], "css": []}], "host_permissions": ["https://*/*", "http://*/*"], "options_page": "options.html", "devtools_page": "devtools.html"}