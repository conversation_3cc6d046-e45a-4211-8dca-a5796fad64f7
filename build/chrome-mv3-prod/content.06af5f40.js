var t,e;"function"==typeof(t=globalThis.define)&&(e=t,t=null),function(e,r,o,n,a){var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{},i="function"==typeof s[n]&&s[n],u=i.cache||{},d="undefined"!=typeof module&&"function"==typeof module.require&&module.require.bind(module);function c(t,r){if(!u[t]){if(!e[t]){var o="function"==typeof s[n]&&s[n];if(!r&&o)return o(t,!0);if(i)return i(t,!0);if(d&&"string"==typeof t)return d(t);var a=Error("Cannot find module '"+t+"'");throw a.code="MODULE_NOT_FOUND",a}p.resolve=function(r){var o=e[t][1][r];return null!=o?o:r},p.cache={};var l=u[t]=new c.Module(t);e[t][0].call(l.exports,p,l,l.exports,this)}return u[t].exports;function p(t){var e=p.resolve(t);return!1===e?{}:c(e)}}c.isParcelRequire=!0,c.Module=function(t){this.id=t,this.bundle=c,this.exports={}},c.modules=e,c.cache=u,c.parent=i,c.register=function(t,r){e[t]=[function(t,e){e.exports=r},{}]},Object.defineProperty(c,"root",{get:function(){return s[n]}}),s[n]=c;for(var l=0;l<r.length;l++)c(r[l]);if(o){var p=c(o);"object"==typeof exports&&"undefined"!=typeof module?module.exports=p:"function"==typeof t&&t.amd?t(function(){return p}):a&&(this[a]=p)}}({"4gaGD":[function(t,e,r){function o(){return`req_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}function n(t){try{chrome.runtime.sendMessage({type:"INTERCEPTED_REQUEST",data:t}).catch(()=>{})}catch(t){console.error("Error sending intercepted request:",t)}}let a=window.fetch;window.fetch=async function(...t){let e;let r=Date.now(),s=o(),[i,u]=t,d="string"==typeof i?i:i.url,c=u?.method||"GET",l={};u?.headers&&(u.headers instanceof Headers?u.headers.forEach((t,e)=>{l[e]=t}):Array.isArray(u.headers)?u.headers.forEach(([t,e])=>{l[t]=e}):Object.entries(u.headers).forEach(([t,e])=>{l[t]=e})),u?.body&&(e="string"==typeof u.body?u.body:u.body instanceof FormData?"[FormData]":u.body instanceof URLSearchParams?u.body.toString():"[Binary Data]");try{let o;let i=await a.apply(this,t),u=i.clone(),p={};i.headers.forEach((t,e)=>{p[e]=t});try{let t=i.headers.get("content-type")||"";o=t.includes("application/json")||t.includes("text/")||t.includes("application/xml")?await u.text():`[${t||"Binary Data"}]`}catch(t){o="[Error reading response body]"}let f={id:s,url:d,method:c,timestamp:r,requestHeaders:l,requestBody:e,responseHeaders:p,responseBody:o,status:i.status,statusText:i.statusText};return n(f),i}catch(o){let t={id:s,url:d,method:c,timestamp:r,requestHeaders:l,requestBody:e,status:0,statusText:o instanceof Error?o.message:"Network Error"};throw n(t),o}};let s=XMLHttpRequest.prototype.open,i=XMLHttpRequest.prototype.send;XMLHttpRequest.prototype.open=function(t,e,...r){return this._interceptorData={id:o(),method:t.toUpperCase(),url:e,timestamp:Date.now()},s.apply(this,[t,e,...r])},XMLHttpRequest.prototype.send=function(t){let e=this._interceptorData;if(!e)return i.apply(this,[t]);t&&("string"==typeof t?e.requestBody=t:t instanceof FormData?e.requestBody="[FormData]":e.requestBody="[Binary Data]"),e.requestHeaders={};let r=this.onreadystatechange;return this.onreadystatechange=function(){if(this.readyState===XMLHttpRequest.DONE){let t;let r={},o=this.getAllResponseHeaders();o&&o.split("\r\n").forEach(t=>{let[e,o]=t.split(": ");e&&o&&(r[e]=o)});try{let e=this.getResponseHeader("content-type")||"";t=e.includes("application/json")||e.includes("text/")||e.includes("application/xml")?this.responseText:`[${e||"Binary Data"}]`}catch(e){t="[Error reading response body]"}let a={...e,responseHeaders:r,responseBody:t,status:this.status,statusText:this.statusText};n(a)}r&&r.apply(this)},i.apply(this,[t])},console.log("API Interceptor content script loaded")},{}]},["4gaGD"],"4gaGD","parcelRequireff35"),globalThis.define=e;